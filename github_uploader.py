#!/usr/bin/env python3
"""
GitHub文件上传器 - 用于托管视频文件
"""

import requests
import base64
import json
from pathlib import Path
from typing import Optional, Tuple
import logging

class GitHubUploader:
    """GitHub文件上传器"""
    
    def __init__(self, token: str, username: str, repo: str):
        """
        初始化GitHub上传器
        
        Args:
            token: GitHub Personal Access Token
            username: GitHub用户名
            repo: 仓库名称
        """
        self.token = token
        self.username = username
        self.repo = repo
        self.base_url = f"https://api.github.com/repos/{username}/{repo}"
        self.headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json"
        }
        self.logger = logging.getLogger(__name__)
    
    def upload_file(self, file_path: Path, remote_path: str = None) -> Optional[str]:
        """
        上传文件到GitHub仓库
        
        Args:
            file_path: 本地文件路径
            remote_path: 远程文件路径（可选，默认使用文件名）
            
        Returns:
            文件的下载URL，失败返回None
        """
        if remote_path is None:
            remote_path = file_path.name
        
        try:
            # 读取文件内容并编码为base64
            with open(file_path, 'rb') as f:
                content = base64.b64encode(f.read()).decode('utf-8')
            
            # 构建API请求
            url = f"{self.base_url}/contents/{remote_path}"
            data = {
                "message": f"Upload {file_path.name}",
                "content": content
            }
            
            self.logger.info(f"正在上传文件到GitHub: {remote_path}")
            
            # 发送请求
            response = requests.put(url, headers=self.headers, json=data, timeout=60)
            
            if response.status_code == 201:
                result = response.json()
                download_url = result['content']['download_url']
                self.logger.info(f"文件上传成功: {download_url}")
                return download_url
            else:
                self.logger.error(f"GitHub上传失败: {response.status_code}, {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"GitHub上传异常: {e}")
            return None
    
    def delete_file(self, remote_path: str) -> bool:
        """
        删除GitHub仓库中的文件
        
        Args:
            remote_path: 远程文件路径
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            # 首先获取文件信息以获得SHA
            url = f"{self.base_url}/contents/{remote_path}"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code != 200:
                self.logger.error(f"获取文件信息失败: {response.status_code}")
                return False
            
            file_info = response.json()
            sha = file_info['sha']
            
            # 删除文件
            data = {
                "message": f"Delete {remote_path}",
                "sha": sha
            }
            
            response = requests.delete(url, headers=self.headers, json=data)
            
            if response.status_code == 200:
                self.logger.info(f"文件删除成功: {remote_path}")
                return True
            else:
                self.logger.error(f"文件删除失败: {response.status_code}, {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除文件异常: {e}")
            return False
    
    def list_files(self, path: str = "") -> list:
        """
        列出仓库中的文件
        
        Args:
            path: 目录路径（可选）
            
        Returns:
            文件列表
        """
        try:
            url = f"{self.base_url}/contents/{path}"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"获取文件列表失败: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"获取文件列表异常: {e}")
            return []

def test_github_uploader():
    """测试GitHub上传功能"""
    print("=== GitHub文件上传测试 ===")
    
    # 配置信息（需要用户填写）
    GITHUB_TOKEN = input("请输入您的GitHub Token: ").strip()
    GITHUB_USERNAME = input("请输入您的GitHub用户名: ").strip()
    GITHUB_REPO = input("请输入仓库名称 (例如: video-hosting): ").strip()
    
    if not all([GITHUB_TOKEN, GITHUB_USERNAME, GITHUB_REPO]):
        print("❌ 配置信息不完整")
        return
    
    # 创建上传器
    uploader = GitHubUploader(GITHUB_TOKEN, GITHUB_USERNAME, GITHUB_REPO)
    
    # 查找视频文件
    video_files = []
    for ext in ['.mp4', '.mov', '.avi']:
        video_files.extend(Path('.').glob(f"*{ext}"))
    
    if not video_files:
        print("❌ 未找到视频文件")
        return
    
    video_file = video_files[0]
    print(f"测试上传文件: {video_file}")
    
    # 检查文件大小
    file_size = video_file.stat().st_size / (1024 * 1024)  # MB
    print(f"文件大小: {file_size:.2f} MB")
    
    if file_size > 25:  # GitHub单文件限制25MB
        print("⚠️ 文件超过25MB，GitHub可能无法上传")
        print("建议使用GitHub Releases或其他服务")
        return
    
    # 上传文件
    download_url = uploader.upload_file(video_file)
    
    if download_url:
        print(f"✅ 上传成功!")
        print(f"下载URL: {download_url}")
        
        # 验证URL是否可访问
        try:
            response = requests.head(download_url, timeout=10)
            if response.status_code == 200:
                print("✅ URL验证成功，可以访问")
            else:
                print(f"⚠️ URL验证失败: {response.status_code}")
        except Exception as e:
            print(f"⚠️ URL验证异常: {e}")
            
        # 询问是否删除文件
        delete = input("\n是否删除上传的测试文件? (y/N): ").strip().lower()
        if delete == 'y':
            if uploader.delete_file(video_file.name):
                print("✅ 测试文件已删除")
            else:
                print("❌ 删除失败")
    else:
        print("❌ 上传失败")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_github_uploader()
