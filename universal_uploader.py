#!/usr/bin/env python3
"""
通用文件上传器 - 支持多个免费文件托管服务
包括: file.io, 0x0.st, catbox.moe, uguu.se 等
"""

import requests
import json
from pathlib import Path
from typing import Optional, Tuple
import logging

class UniversalUploader:
    """通用文件上传器，支持多个服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def upload_to_file_io(self, file_path: Path) -> Optional[str]:
        """上传到file.io (一次性下载)"""
        try:
            with open(file_path, 'rb') as f:
                files = {'file': f}
                response = requests.post('https://file.io', files=files, timeout=120)
                
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    url = result.get('link')
                    self.logger.info(f"file.io上传成功: {url}")
                    return url
            
            self.logger.error(f"file.io上传失败: {response.text}")
            return None
            
        except Exception as e:
            self.logger.error(f"file.io上传异常: {e}")
            return None
    
    def upload_to_0x0(self, file_path: Path) -> Optional[str]:
        """上传到0x0.st (临时存储)"""
        try:
            with open(file_path, 'rb') as f:
                files = {'file': f}
                response = requests.post('https://0x0.st', files=files, timeout=120)
                
            if response.status_code == 200:
                url = response.text.strip()
                if url.startswith('https://'):
                    self.logger.info(f"0x0.st上传成功: {url}")
                    return url
            
            self.logger.error(f"0x0.st上传失败: {response.text}")
            return None
            
        except Exception as e:
            self.logger.error(f"0x0.st上传异常: {e}")
            return None
    
    def upload_to_catbox(self, file_path: Path) -> Optional[str]:
        """上传到catbox.moe (长期存储)"""
        try:
            with open(file_path, 'rb') as f:
                files = {'fileToUpload': f}
                data = {'reqtype': 'fileupload'}
                response = requests.post('https://catbox.moe/user/api.php', 
                                       files=files, data=data, timeout=120)
                
            if response.status_code == 200:
                url = response.text.strip()
                if url.startswith('https://'):
                    self.logger.info(f"catbox.moe上传成功: {url}")
                    return url
            
            self.logger.error(f"catbox.moe上传失败: {response.text}")
            return None
            
        except Exception as e:
            self.logger.error(f"catbox.moe上传异常: {e}")
            return None
    
    def upload_to_uguu(self, file_path: Path) -> Optional[str]:
        """上传到uguu.se (临时存储)"""
        try:
            with open(file_path, 'rb') as f:
                files = {'files[]': f}
                response = requests.post('https://uguu.se/upload.php', files=files, timeout=120)
                
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    url = result['files'][0]['url']
                    self.logger.info(f"uguu.se上传成功: {url}")
                    return url
            
            self.logger.error(f"uguu.se上传失败: {response.text}")
            return None
            
        except Exception as e:
            self.logger.error(f"uguu.se上传异常: {e}")
            return None
    
    def upload_to_transfer_sh(self, file_path: Path) -> Optional[str]:
        """上传到transfer.sh (14天存储)"""
        try:
            with open(file_path, 'rb') as f:
                response = requests.put(
                    f'https://transfer.sh/{file_path.name}',
                    data=f,
                    timeout=120
                )
                
            if response.status_code == 200:
                url = response.text.strip()
                self.logger.info(f"transfer.sh上传成功: {url}")
                return url
            
            self.logger.error(f"transfer.sh上传失败: {response.text}")
            return None
            
        except Exception as e:
            self.logger.error(f"transfer.sh上传异常: {e}")
            return None
    
    def upload_with_fallback(self, file_path: Path) -> Tuple[Optional[str], str]:
        """尝试多个服务，返回第一个成功的URL"""
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        
        # 根据文件大小选择合适的服务
        if file_size_mb > 100:
            services = [
                ("transfer.sh", self.upload_to_transfer_sh),
                ("0x0.st", self.upload_to_0x0),
            ]
        else:
            services = [
                ("catbox.moe", self.upload_to_catbox),
                ("file.io", self.upload_to_file_io),
                ("uguu.se", self.upload_to_uguu),
                ("0x0.st", self.upload_to_0x0),
                ("transfer.sh", self.upload_to_transfer_sh),
            ]
        
        for service_name, upload_func in services:
            self.logger.info(f"尝试上传到 {service_name}...")
            print(f"  尝试上传到 {service_name}...")
            
            url = upload_func(file_path)
            if url:
                print(f"  ✅ {service_name} 上传成功!")
                return url, service_name
            else:
                print(f"  ❌ {service_name} 上传失败")
        
        return None, "所有服务都失败"

def test_upload():
    """测试上传功能"""
    print("=== 通用文件上传测试 ===")
    
    uploader = UniversalUploader()
    
    # 查找视频文件
    video_files = []
    for ext in ['.mp4', '.mov', '.avi', '.mkv']:
        video_files.extend(Path('.').glob(f"*{ext}"))
    
    if not video_files:
        print("❌ 未找到视频文件")
        return
    
    video_file = video_files[0]
    print(f"测试上传文件: {video_file}")
    
    # 检查文件大小
    file_size = video_file.stat().st_size / (1024 * 1024)  # MB
    print(f"文件大小: {file_size:.2f} MB")
    
    if file_size > 200:
        print("⚠️ 文件较大，上传可能需要较长时间")
    
    # 尝试上传
    url, service = uploader.upload_with_fallback(video_file)
    
    if url:
        print(f"\n✅ 上传成功!")
        print(f"服务: {service}")
        print(f"URL: {url}")
        
        # 验证URL是否可访问
        try:
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                print("✅ URL验证成功，可以访问")
                return url
            else:
                print(f"⚠️ URL验证失败: {response.status_code}")
                return url  # 仍然返回URL，可能是服务器不支持HEAD请求
        except Exception as e:
            print(f"⚠️ URL验证异常: {e}")
            return url  # 仍然返回URL
            
    else:
        print(f"❌ 上传失败: {service}")
        return None

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_upload()
