# 视频分析程序API迁移说明

## 概述
已成功将视频分析程序从Google Gemini API迁移到火山引擎豆包API。

## 主要修改内容

### 1. 依赖库更改
- **移除**: `google.generativeai as genai`
- **添加**: `from openai import OpenAI` 和 `import base64`

### 2. API配置更改
```python
# 原Gemini配置
API_KEY = "AIzaSyC_iGlomtp5jlMxduCcEI_uWrqfB4Xq2k0"
MODEL_NAME = "gemini-2.5-pro"

# 新豆包配置
API_KEY = "3b013cb6-8f55-4432-9deb-595c2fa7256b"
MODEL_NAME = "doubao-seed-1-6-250615"
```

### 3. 核心函数重写
- **函数名**: `analyze_video_with_gemini` → `analyze_video_with_doubao`
- **API调用方式**: 完全重写以适配火山引擎API格式

### 4. 视频处理方式更改
- **Gemini方式**: 上传文件到服务器，等待处理完成
- **豆包方式**: 将视频文件转换为base64编码，直接在请求中发送

### 5. 请求格式更改
```python
# 豆包API请求格式
messages = [
    {
        "role": "user",
        "content": [
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:video/{video_format};base64,{video_base64}"
                }
            },
            {
                "type": "text",
                "text": DOUBAO_PROMPT
            }
        ]
    }
]

response = client.chat.completions.create(
    model=MODEL_NAME,
    messages=messages
)
```

### 6. Token统计更改
```python
# 豆包API的token统计
if hasattr(response, 'usage') and response.usage:
    local_input_tokens = getattr(response.usage, 'prompt_tokens', 0)
    local_output_tokens = getattr(response.usage, 'completion_tokens', 0)
```

## 测试验证
- 创建了 `test_doubao_api.py` 测试脚本
- API连接测试成功
- Token统计功能正常

## 注意事项
1. 火山引擎豆包API使用OpenAI兼容的接口格式
2. 视频文件需要转换为base64格式发送
3. API密钥已更新为您提供的火山引擎密钥
4. 保持了原有的提示词和输出格式要求

## 使用方法
程序使用方法与之前完全相同，只是底层API调用已切换到火山引擎豆包。
