#!/usr/bin/env python3
"""
测试火山引擎豆包API的视频URL分析功能
"""

import os
import time
import threading
import http.server
import socketserver
import socket
import requests
from pathlib import Path
from urllib.parse import quote

# 火山引擎配置
API_KEY = "3b013cb6-8f55-4432-9deb-595c2fa7256b"
MODEL_NAME = "doubao-seed-1-6-250615"

def find_free_port(start_port: int = 8000) -> int:
    """找到一个可用的端口"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    raise RuntimeError("无法找到可用端口")

def start_http_server(directory: Path) -> tuple[int, threading.Thread]:
    """启动HTTP服务器来托管视频文件"""
    port = find_free_port(8000)
    
    class QuietHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def log_message(self, format, *args):
            # 禁用HTTP服务器的日志输出
            pass
    
    original_cwd = os.getcwd()
    os.chdir(directory)
    
    server = socketserver.TCPServer(("", port), QuietHTTPRequestHandler)
    
    def serve_forever():
        server.serve_forever()
    
    server_thread = threading.Thread(target=serve_forever, daemon=True)
    server_thread.start()
    
    # 恢复原始工作目录
    os.chdir(original_cwd)
    
    print(f"HTTP服务器已启动在端口 {port}")
    return port, server_thread

def test_video_analysis():
    """测试视频分析功能"""
    try:
        # 查找视频文件
        current_dir = Path(".")
        video_files = []
        for ext in ['.mp4', '.mov', '.avi']:
            video_files.extend(current_dir.glob(f"*{ext}"))
        
        if not video_files:
            print("未找到视频文件")
            return False
        
        video_file = video_files[0]
        print(f"使用视频文件: {video_file}")
        
        # 启动HTTP服务器
        server_port, server_thread = start_http_server(current_dir)
        
        # 构建视频URL
        video_filename = quote(video_file.name)
        video_url = f"http://localhost:{server_port}/{video_filename}"
        
        print(f"视频URL: {video_url}")
        
        # 构建API请求
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }

        # 使用正确的video_url格式
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请分析这个视频的内容，描述其中的画面、动作和产品功能："
                        },
                        {
                            "type": "video_url",
                            "video_url": {
                                "url": video_url
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000
        }

        print("发送API请求...")
        
        # 发送API请求
        response = requests.post(
            "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
            json=payload,
            headers=headers,
            timeout=60
        )

        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API调用成功！")
            print(f"分析结果: {result['choices'][0]['message']['content']}")
            
            if "usage" in result:
                print(f"Token使用 - 输入: {result['usage'].get('prompt_tokens', 0)}, 输出: {result['usage'].get('completion_tokens', 0)}")
            
            return True
        else:
            print(f"API调用失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    print("测试火山引擎豆包API视频分析功能...")
    success = test_video_analysis()
    if success:
        print("✅ 测试成功！")
    else:
        print("❌ 测试失败！")
