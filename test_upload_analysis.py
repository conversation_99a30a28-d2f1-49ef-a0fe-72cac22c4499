#!/usr/bin/env python3
"""
测试上传+分析的完整流程
"""

import requests
import json
from pathlib import Path

# 火山引擎配置
API_KEY = "3b013cb6-8f55-4432-9deb-595c2fa7256b"
MODEL_NAME = "doubao-seed-1-6-250615"

# 简化的提示词
PROMPT = """请分析这个视频的内容，包括：
1. 画面中的主要内容
2. 人物动作和行为
3. 产品功能展示
4. 场景环境描述
请用中文回答。"""

def upload_video(video_file: Path) -> str:
    """上传视频到file.io并返回URL"""
    print(f"正在上传视频: {video_file.name}")
    
    try:
        with open(video_file, 'rb') as f:
            files = {'file': f}
            response = requests.post('https://file.io', files=files, timeout=60)
            
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                url = result.get('link')
                print(f"✅ 上传成功: {url}")
                return url
        
        print(f"❌ 上传失败: {response.text}")
        return None
        
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def analyze_video(video_url: str) -> str:
    """使用豆包API分析视频"""
    print("正在调用豆包API分析视频...")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }

    payload = {
        "model": MODEL_NAME,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": PROMPT
                    },
                    {
                        "type": "video_url",
                        "video_url": {
                            "url": video_url
                        }
                    }
                ]
            }
        ],
        "max_tokens": 2000
    }

    try:
        response = requests.post(
            "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
            json=payload,
            headers=headers,
            timeout=180
        )

        if response.status_code == 200:
            result = response.json()
            analysis = result["choices"][0]["message"]["content"]
            
            # 显示token使用情况
            if "usage" in result:
                usage = result["usage"]
                print(f"Token使用 - 输入: {usage.get('prompt_tokens', 0)}, 输出: {usage.get('completion_tokens', 0)}")
            
            return analysis
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return None

def main():
    """主函数"""
    print("=== 测试视频上传+分析流程 ===")
    
    # 查找视频文件
    video_files = []
    for ext in ['.mp4', '.mov', '.avi']:
        video_files.extend(Path('.').glob(f"*{ext}"))
    
    if not video_files:
        print("❌ 未找到视频文件")
        return
    
    video_file = video_files[0]
    print(f"使用视频文件: {video_file}")
    
    # 检查文件大小
    file_size = video_file.stat().st_size / (1024 * 1024)  # MB
    print(f"文件大小: {file_size:.2f} MB")
    
    if file_size > 50:
        print("⚠️ 文件较大，处理时间可能较长")
    
    # 步骤1: 上传视频
    video_url = upload_video(video_file)
    if not video_url:
        return
    
    # 步骤2: 分析视频
    analysis = analyze_video(video_url)
    if analysis:
        print("\n✅ 分析成功!")
        print("=" * 50)
        print("分析结果:")
        print(analysis)
        print("=" * 50)
        
        # 保存结果
        output_file = video_file.with_suffix('.analysis.txt')
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"视频文件: {video_file}\n")
            f.write(f"上传URL: {video_url}\n")
            f.write(f"分析结果:\n{analysis}")
        
        print(f"结果已保存到: {output_file}")
    else:
        print("❌ 分析失败")

if __name__ == "__main__":
    main()
