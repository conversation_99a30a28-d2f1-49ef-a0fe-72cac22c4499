2025-08-18 19:39:14,084 - INFO - 开始执行视频分析脚本...
2025-08-18 19:39:14,085 - INFO - 实时日志文件: log/video_analysis_realtime_20250818_193914.log
2025-08-18 19:39:14,085 - INFO - 结果日志文件: log/video_analysis_results_20250818_193914.log
2025-08-18 19:39:14,085 - INFO - 豆包 API 配置完成
2025-08-18 19:39:14,086 - INFO - 开始为原始文件 '原子工作台功能9.mp4' 使用豆包 API 分析视频...
2025-08-18 19:39:14,148 - INFO -   正在从视频中提取关键帧: 原子工作台功能9.mp4
2025-08-18 19:39:21,057 - INFO -   成功提取 5 个关键帧，开始调用豆包API...
2025-08-18 19:39:56,011 - INFO - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-08-18 19:39:56,039 - INFO -   Token使用 - 输入: 7922, 输出: 1262
2025-08-18 19:39:56,040 - INFO -   成功接收到 '原子工作台功能9.mp4' 的分析结果
2025-08-18 19:39:56,130 - INFO - 开始为原始文件 '原子工作台功能10.mp4' 使用豆包 API 分析视频...
2025-08-18 19:39:56,230 - INFO -   正在从视频中提取关键帧: 原子工作台功能10.mp4
2025-08-18 19:40:03,643 - INFO -   成功提取 5 个关键帧，开始调用豆包API...
2025-08-18 19:41:01,741 - INFO - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-08-18 19:41:01,747 - INFO -   Token使用 - 输入: 7923, 输出: 2038
2025-08-18 19:41:01,747 - INFO -   成功接收到 '原子工作台功能10.mp4' 的分析结果
2025-08-18 19:41:01,753 - INFO - 开始为原始文件 '原子工作台功能6.mp4' 使用豆包 API 分析视频...
2025-08-18 19:41:01,804 - INFO -   正在从视频中提取关键帧: 原子工作台功能6.mp4
2025-08-18 19:41:07,565 - INFO -   成功提取 5 个关键帧，开始调用豆包API...
2025-08-18 19:41:30,822 - INFO - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-08-18 19:41:30,826 - INFO -   Token使用 - 输入: 7922, 输出: 853
2025-08-18 19:41:30,826 - INFO -   成功接收到 '原子工作台功能6.mp4' 的分析结果
2025-08-18 19:41:30,838 - INFO - 开始为原始文件 '原子工作台功能5.mp4' 使用豆包 API 分析视频...
2025-08-18 19:41:30,907 - INFO -   正在从视频中提取关键帧: 原子工作台功能5.mp4
2025-08-18 19:41:35,023 - INFO -   成功提取 5 个关键帧，开始调用豆包API...
2025-08-18 19:42:05,115 - INFO - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-08-18 19:42:05,137 - INFO -   Token使用 - 输入: 7922, 输出: 970
2025-08-18 19:42:05,138 - INFO -   成功接收到 '原子工作台功能5.mp4' 的分析结果
2025-08-18 19:42:05,146 - INFO - 开始为原始文件 '原子工作台功能1.mp4' 使用豆包 API 分析视频...
2025-08-18 19:42:05,211 - INFO -   正在从视频中提取关键帧: 原子工作台功能1.mp4
2025-08-18 19:42:09,701 - INFO -   成功提取 5 个关键帧，开始调用豆包API...
2025-08-18 19:42:39,851 - INFO - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-08-18 19:42:39,860 - INFO -   Token使用 - 输入: 7922, 输出: 1027
2025-08-18 19:42:39,860 - INFO -   成功接收到 '原子工作台功能1.mp4' 的分析结果
2025-08-18 19:42:39,863 - INFO - 开始为原始文件 '原子工作台功能2.mp4' 使用豆包 API 分析视频...
2025-08-18 19:42:39,926 - INFO -   正在从视频中提取关键帧: 原子工作台功能2.mp4
2025-08-18 19:42:45,870 - INFO -   成功提取 5 个关键帧，开始调用豆包API...
2025-08-18 19:43:53,140 - INFO - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-08-18 19:43:53,156 - INFO -   Token使用 - 输入: 7922, 输出: 2180
2025-08-18 19:43:53,157 - INFO -   成功接收到 '原子工作台功能2.mp4' 的分析结果
2025-08-18 19:43:53,162 - INFO - 
==================================================
2025-08-18 19:43:53,162 - INFO - 运行统计信息:
2025-08-18 19:43:53,163 - INFO - 总耗时: 279.08 秒
2025-08-18 19:43:53,163 - INFO - 总输入Token量: 47533
2025-08-18 19:43:53,163 - INFO - 总输出Token量: 8330
2025-08-18 19:43:53,163 - INFO - ==================================================
