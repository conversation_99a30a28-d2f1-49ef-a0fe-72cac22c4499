#!/usr/bin/env python3
"""
测试火山引擎豆包API的简单脚本
"""

import os
from openai import OpenAI

# 火山引擎配置
API_KEY = "3b013cb6-8f55-4432-9deb-595c2fa7256b"
MODEL_NAME = "doubao-seed-1-6-250615"

def test_doubao_api():
    """测试豆包API连接"""
    try:
        # 初始化火山引擎客户端
        client = OpenAI(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key=API_KEY,
        )

        # 简单的文本测试
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {
                    "role": "user",
                    "content": "你好，请简单介绍一下你自己。"
                }
            ]
        )

        print("API连接成功！")
        print(f"响应: {response.choices[0].message.content}")
        
        # 检查token使用情况
        if hasattr(response, 'usage') and response.usage:
            print(f"Token使用 - 输入: {response.usage.prompt_tokens}, 输出: {response.usage.completion_tokens}")
        
        return True
        
    except Exception as e:
        print(f"API连接失败: {e}")
        return False

if __name__ == "__main__":
    print("测试火山引擎豆包API连接...")
    test_doubao_api()
