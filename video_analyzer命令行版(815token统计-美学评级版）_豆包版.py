import os
import time
import subprocess # Added for ffmpeg
from pathlib import Path
from openai import OpenAI
import logging
import threading
from datetime import datetime
import base64
import http.server
import socketserver
import socket
from urllib.parse import quote
import requests

# --- Configuration ---
# 使用火山引擎的 API 密钥
API_KEY = "3b013cb6-8f55-4432-9deb-595c2fa7256b"
# 使用火山引擎的模型名称 - 支持视频分析的模型
MODEL_NAME = "doubao-seed-1-6-250615"
# HTTP服务器配置
HTTP_SERVER_PORT = 8000
HTTP_SERVER_HOST = "localhost"
# 常见的视频文件扩展名列表
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.avi', '.mkv', '.flv', '.wmv', '.mpeg', '.mpg', '.m4v' ] # Added .m4v

# --- 统计变量 ---
total_start_time = None
total_input_tokens = 0
total_output_tokens = 0
# 线程锁
token_lock = threading.Lock()
results_log_lock = threading.Lock()
progress_lock = threading.Lock()
completed_tasks = 0
failed_tasks = 0



# 豆包 API 分析视频的提示词
DOUBAO_PROMPT = """### 角色
你是一个视频内容分析助手，会对输入的视频素材进行内容理解后，按照“输出格式要求”输出最终结果。

### 任务
接收视频后，请按照以下规则详细解析视频内容。
你的输出将**仅仅是一个单一的、完整的 JSON 数组**。
JSON 数组中的每个对象代表一个镜头，其 `description` 字段需要包含对该镜头的详细分析，包括主体、环境、语义和可选的技术细节。

### 输出格式要求 (非常重要)
你的输出**必须且仅能是**一个单一的、完整的、纯文本的 JSON 数组。
- **绝对不要在 JSON 数组前后添加任何额外的文字、解释、注释或markdown代码块标记 (例如 ```json ... ``` 或 ``` ... ```)。直接输出纯 JSON 数组，该数组应以 `[` 开始，并以 `]` 结束。**
- JSON 数组中的每个对象代表一个镜头，必须包含以下字段，且类型需严格匹配：
  - `start`: 数字 (镜头开始时间，单位：秒，整数)
  - `end`: 数字 (镜头结束时间，单位：秒，整数)
  - `duration`: 数字 (镜头持续时间，单位：秒，整数)
  - `description`: 字符串 (对镜头的详细描述。此描述应整合对画面主体、环境、语义以及可选的技术细节的分析。可以使用换行符 `\n` 来组织描述内容，使其更易读。)
  - `tags`: 字符串数组 (描述镜头的关键词标签，例如 `["标签1", "标签2"]`)
  - `level`: 字符串 (美学评级，，范围从1到10，1表示极差，10表示极佳。考虑构图、色彩、光影、情感表达等因素。)
  - `skip`: 布尔值 (`true` 或 `false`，指示是否建议跳过此镜头)
- JSON 示例如下（此示例仅用于展示结构和 `description` 的详细程度，实际内容需根据视频分析结果填写）：
  ```json
  [
    {
      "start": 0,
      "end": 8,
      "duration": 8,
      "description": "主持人在演播室中央讲话，LED 蓝色背景，冷光。",
      "tags": ["男性主持人","室内","演播室","中景"],
      "level": "8",
      "skip": false
    },
    {
      "start": 8,
      "end": 9,
      "duration": 1,
      "description": "镜头在演播室中，画面紧接着转移到在沙滩上。",
      "tags": [],
      "level": "5",
      "skip": true
    }
  ]
  ```

### 内容解析规则
1. 以秒为间隔分切视频时间，timestamps描述为hh:mm:ss，如 片段1：hh:mm:ss - hh:mm:ss+1。在自然语言描述中，镜头时间范围应准确反映此规则。
2. 详细分析hh:mm:ss - hh:mm:ss+1 的内容，包括：
   - 视频图像主体：清晰识别并详细描述画面中的主要人物、物体或焦点，包括其性别、年龄、大致职业或身份特征、服饰、姿态、表情、动作、朝向、与画面边界的空间位置关系等。若画面中有多位主体，分别描述并指出主次关系。
   - 视频画面环境：描述该帧画面的拍摄地点（如可判断：室内/室外、城市/自然、具体建筑或场景类型）、时间线索（如白天/夜晚、阴晴、灯光类型）、光线方向和强度、色调氛围、构图特征（如三分构图、对称结构）等整体环境元素。
   - 画面内容语义：综合分析主体与环境之间的关系，如：主体正在进行的行为意图、情绪状态、可能的剧情上下文，是否存在互动、冲突、对比或呼应。注意：此项用于建立画面在故事中的语义信息。
   - 美学评级：对画面美学进行主观评级，范围从1到10，1表示极差，10表示极佳。考虑构图、色彩、光影、情感表达等因素。
3. hh:mm:ss - hh:mm:ss+1 为同一个画面内容的固定镜头时，合并为同一个镜头描述其内容，并标注该镜头在整个视频中的绝对开始时间和绝对结束时间以及镜头持续时间。JSON 中的 `start`, `end`, `duration` 字段应反映此合并后的时间。
4. hh:mm:ss - hh:mm:ss+1 为包含场景转换的长镜头时，详细描述镜头开始时间到结束时间的每2秒的内容分别是什么内容，并标出长镜头的持续时间。自然语言描述应体现此细节。
5. hh:mm:ss - hh:mm:ss+1 1秒内的画面恰好横跨两个镜头，则标注“跨转场镜头”，且标出该镜头在整个视频中的绝对开始时间和绝对结束时间以及镜头持续时间，并在 JSON 中标记 `skip: true`。

### 质量要求
- 描述应精确且凝练，避免重复与无信息量词汇。
- 若无法确定信息，请使用“未知”或给出最可能推测并加“（推测）”。
- **再次强调：严格遵循上述“输出格式要求”进行输出。自然语言描述在前，纯JSON数组在后，两者之间仅有一个换行符，无任何其他多余字符或标记。**
"""

# --- Helper Functions ---

# HTTP服务器相关变量
http_server = None
http_server_thread = None

def find_free_port(start_port: int = 8000) -> int:
    """找到一个可用的端口"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    raise RuntimeError("无法找到可用端口")

def start_http_server(directory: Path) -> tuple[int, threading.Thread]:
    """启动HTTP服务器来托管视频文件"""
    global http_server, http_server_thread

    port = find_free_port(HTTP_SERVER_PORT)

    class QuietHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def log_message(self, format, *args):
            # 禁用HTTP服务器的日志输出
            pass

    os.chdir(directory)
    http_server = socketserver.TCPServer(("", port), QuietHTTPRequestHandler)

    def serve_forever():
        http_server.serve_forever()

    http_server_thread = threading.Thread(target=serve_forever, daemon=True)
    http_server_thread.start()

    logging.info(f"HTTP服务器已启动在端口 {port}")
    return port, http_server_thread

def stop_http_server():
    """停止HTTP服务器"""
    global http_server
    if http_server:
        http_server.shutdown()
        http_server.server_close()
        logging.info("HTTP服务器已停止")

def create_results_logger(results_log_file: Path) -> logging.Logger:
    """创建仅写入结果日志文件的 logger（不输出到控制台）。"""
    logger = logging.getLogger("results_logger")
    logger.setLevel(logging.INFO)
    # 清理已有的 handlers 以避免重复
    for h in list(logger.handlers):
        logger.removeHandler(h)
    fh = logging.FileHandler(results_log_file, encoding='utf-8')
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    logger.addHandler(fh)
    logger.propagate = False  # 不向根 logger 传播
    return logger

def find_video_files(directory: Path) -> list[Path]:
    """在指定目录及其子目录中查找视频文件。"""
    video_files = []
    print(f"正在扫描目录: {directory.resolve()}")
    for root, _, files in os.walk(directory):
        for file in files:
            file_path = Path(root) / file
            if file_path.suffix.lower() in VIDEO_EXTENSIONS:
                video_files.append(file_path)
    return video_files

def clear_proxy_environment():
    """清除代理环境变量，因为访问豆包大模型不需要代理"""
    proxy_keys = ['https_proxy', 'http_proxy', 'all_proxy', 'HTTPS_PROXY', 'HTTP_PROXY', 'ALL_PROXY']

    for key in proxy_keys:
        if key in os.environ:
            del os.environ[key]
            print(f"已清除代理环境变量: {key}")

    print("代理环境变量已清除，直接访问豆包API")

def setup_logging():
    """设置日志系统: 拆分为实时日志与结果日志"""
    log_dir = Path("./log")
    log_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    realtime_log_file = log_dir / f"video_analysis_realtime_{timestamp}.log"
    results_log_file = log_dir / f"video_analysis_results_{timestamp}.log"

    # 配置根日志到 实时日志 + 控制台
    for h in list(logging.root.handlers):
        logging.root.removeHandler(h)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(realtime_log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return realtime_log_file, results_log_file

def upload_video_to_service(video_file: Path) -> tuple[str | None, str]:
    """上传视频到临时文件服务"""
    try:
        # 尝试file.io服务
        with open(video_file, 'rb') as f:
            files = {'file': f}
            response = requests.post('https://file.io', files=files, timeout=60)

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                url = result.get('link')
                logging.info(f"文件已上传到file.io: {url}")
                return url, "file.io"

        # 如果file.io失败，尝试0x0.st
        with open(video_file, 'rb') as f:
            files = {'file': f}
            response = requests.post('https://0x0.st', files=files, timeout=60)

        if response.status_code == 200:
            url = response.text.strip()
            logging.info(f"文件已上传到0x0.st: {url}")
            return url, "0x0.st"

        return None, "所有上传服务都失败"

    except Exception as e:
        return None, f"上传异常: {e}"

def analyze_video_with_doubao(video_file_to_upload: Path, original_file_name_for_display: str, use_upload: bool = True) -> tuple[str | None, str | None, int, int]:
    """使用火山引擎豆包 API 通过视频URL分析单个视频文件，返回(结果, 错误, 输入token, 输出token)"""
    global total_input_tokens, total_output_tokens

    local_input_tokens = 0
    local_output_tokens = 0

    logging.info(f"开始为原始文件 '{original_file_name_for_display}' 使用豆包 API 分析视频...")

    try:
        if use_upload:
            # 上传到临时文件服务
            logging.info(f"  正在上传视频到临时文件服务...")
            video_url, upload_service = upload_video_to_service(video_file_to_upload)
            if not video_url:
                return None, f"视频上传失败: {upload_service}", 0, 0
            logging.info(f"  视频已上传到 {upload_service}: {video_url}")
        else:
            # 使用本地HTTP服务器（备用方案）
            server_port = getattr(analyze_video_with_doubao, 'server_port', 8000)
            video_filename = quote(video_file_to_upload.name)
            video_url = f"http://{HTTP_SERVER_HOST}:{server_port}/{video_filename}"
            logging.info(f"  使用本地服务器URL: {video_url}")

        # 使用requests直接调用API
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }

        # 构建请求参数 - 使用正确的video_url格式
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": DOUBAO_PROMPT
                        },
                        {
                            "type": "video_url",
                            "video_url": {
                                "url": video_url
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 4000
        }

        logging.info(f"  开始调用豆包API分析视频...")

        # 发送API请求
        response = requests.post(
            "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
            json=payload,
            headers=headers,
            timeout=180  # 3分钟超时
        )

        if response.status_code == 200:
            result = response.json()

            # 提取分析结果
            analysis_result = result["choices"][0]["message"]["content"]

            # 统计token使用量
            if "usage" in result:
                local_input_tokens = result["usage"].get("prompt_tokens", 0)
                local_output_tokens = result["usage"].get("completion_tokens", 0)
                with token_lock:
                    total_input_tokens += local_input_tokens
                    total_output_tokens += local_output_tokens
                logging.info(f"  Token使用 - 输入: {local_input_tokens}, 输出: {local_output_tokens}")

            logging.info(f"  成功接收到 '{original_file_name_for_display}' 的分析结果")
            return analysis_result, None, local_input_tokens, local_output_tokens
        else:
            error_msg = f"API请求失败: {response.status_code}, {response.text}"
            logging.error(f"  错误: {error_msg}")
            return None, error_msg, 0, 0

    except Exception as e:
        error_msg = f"处理视频 '{original_file_name_for_display}' 时发生错误: {e}"
        logging.error(f"  错误: {error_msg}")
        return None, error_msg, 0, 0

def process_single_video(video_path_original: Path, results_logger: logging.Logger, total_count: int) -> dict:
    """处理单个视频文件：可选转换、调用Gemini分析、保存结果，并将结果作为模块化块写入结果日志。"""
    start_ts = time.time()
    original_display_name = video_path_original.name
    output_dir = video_path_original.parent
    converted_mp4_path: Path | None = None
    video_to_process = video_path_original
    perfile_input_tokens = 0
    perfile_output_tokens = 0
    success = False
    error_message = None
    output_file_path: Path | None = None

    # 子进度: 10% 开始
    logging.info(f"[子进度 10%] 开始处理: {original_display_name}")

    # 如果是 m4v，先尝试无损转 mp4
    if video_path_original.suffix.lower() == '.m4v':
        logging.info(f"  检测到 M4V 文件，尝试转换: {original_display_name}")
        converted_mp4_path = video_path_original.with_name(f"{video_path_original.stem}_converted_to.mp4")
        ffmpeg_command = [
            "ffmpeg", "-y",
            "-i", str(video_path_original),
            "-c:v", "copy",
            "-c:a", "copy",
            str(converted_mp4_path)
        ]
        try:
            subprocess.run(ffmpeg_command, check=True, capture_output=True, text=True, encoding='utf-8')
            logging.info(f"  转换成功 -> {converted_mp4_path}")
            video_to_process = converted_mp4_path
            logging.info(f"[子进度 25%] 转换完成: {original_display_name}")
        except subprocess.CalledProcessError as e:
            error_message = f"FFmpeg 转换失败: {original_display_name}. Error: {e.stderr}"
            logging.error(error_message)
            error_file_path = output_dir / f"{video_path_original.stem}_conversion_error.txt"
            try:
                with open(error_file_path, "w", encoding="utf-8") as f:
                    f.write(f"FFmpeg 转换视频 {original_display_name} 时出错:\n{e.stderr}")
            except IOError as e_io:
                logging.error(f"无法写入 FFmpeg 错误文件 {error_file_path}: {e_io}")
            # 写入结果日志块
            duration = time.time() - start_ts
            block = [
                f"===== 文件: {original_display_name} =====",
                f"路径: {video_path_original.resolve()}",
                f"结果: 失败 (转换失败)",
                f"错误: {error_message}",
                f"用时: {duration:.2f}s",
                "===== 结束 ====="
            ]
            with results_log_lock:
                results_logger.info("\n".join(block))
            return {"success": False, "error": error_message}
        except FileNotFoundError:
            error_message = f"FFmpeg 未找到。请安装并加入 PATH，跳过转换 {original_display_name}。"
            logging.error(error_message)
            error_file_path = output_dir / f"{video_path_original.stem}_ffmpeg_not_found.txt"
            try:
                with open(error_file_path, "w", encoding="utf-8") as f:
                    f.write(error_message)
            except IOError as e_io:
                logging.error(f"无法写入 FFmpeg 未找到错误文件 {error_file_path}: {e_io}")
            # 写入结果日志块
            duration = time.time() - start_ts
            block = [
                f"===== 文件: {original_display_name} =====",
                f"路径: {video_path_original.resolve()}",
                f"结果: 失败 (FFmpeg 未找到)",
                f"错误: {error_message}",
                f"用时: {duration:.2f}s",
                "===== 结束 ====="
            ]
            with results_log_lock:
                results_logger.info("\n".join(block))
            return {"success": False, "error": error_message}

    # 调用豆包分析 - 使用文件上传方式
    logging.info(f"[子进度 40%] 开始分析: {original_display_name}")
    analysis_result, error_message, perfile_input_tokens, perfile_output_tokens = analyze_video_with_doubao(video_to_process, original_display_name, use_upload=True)

    if error_message:
        logging.error(f"分析失败: {original_display_name} -> {error_message}")
        error_file_path = output_dir / f"{video_path_original.stem}_analysis_error.txt"
        try:
            with open(error_file_path, "w", encoding="utf-8") as f:
                f.write(f"分析视频 {original_display_name} (处理文件: {video_to_process.name}) 时出错:\n{error_message}")
        except IOError as e_io:
            logging.error(f"无法写入分析错误文件 {error_file_path}: {e_io}")
        success = False
    elif analysis_result:
        logging.info(f"[子进度 80%] 分析完成，写入结果: {original_display_name}")
        output_file_path = output_dir / f"{video_path_original.stem}_analysis.json"
        try:
            with open(output_file_path, "w", encoding="utf-8") as f:
                f.write(f"文件路径: {video_path_original.resolve()}\n")
                f.write(analysis_result)
            logging.info(f"结果已保存: {output_file_path}")
            success = True
        except IOError as e_io:
            logging.error(f"写入分析结果失败 {output_file_path}: {e_io}")
            success = False
            error_message = str(e_io)
    else:
        logging.warning(f"未收到分析结果: {original_display_name}")
        empty_file_path = output_dir / f"{video_path_original.stem}_analysis_empty.txt"
        try:
            with open(empty_file_path, "w", encoding="utf-8") as f:
                f.write(f"未从 Gemini 收到原始视频 {original_display_name} (处理文件: {video_to_process.name}) 的分析结果。")
        except IOError as e_io:
            logging.error(f"无法写入空分析记录文件 {empty_file_path}: {e_io}")
        success = False

    # 清理转换后的临时文件
    if converted_mp4_path and converted_mp4_path.exists():
        try:
            os.remove(converted_mp4_path)
            logging.info(f"已清理转换后的文件: {converted_mp4_path}")
        except OSError as e:
            logging.warning(f"无法删除转换后的文件 {converted_mp4_path}: {e}")

    # 写入结果日志（缓冲->一次性写入）
    duration = time.time() - start_ts
    status_text = "成功" if success else "失败"
    block_lines = [
        f"===== 文件: {original_display_name} =====",
        f"路径: {video_path_original.resolve()}",
        f"处理文件: {video_to_process.resolve()}",
        f"结果: {status_text}",
        f"输入Token: {perfile_input_tokens} | 输出Token: {perfile_output_tokens}",
        f"输出结果文件: {output_file_path if output_file_path else '无'}",
        f"错误: {error_message if error_message else '无'}",
        f"用时: {duration:.2f}s",
        "===== 结束 =====",
    ]
    with results_log_lock:
        results_logger.info("\n".join(block_lines))

    # 100% 完成
    logging.info(f"[子进度 100%] 完成: {original_display_name} -> {status_text}")

    return {
        "success": success,
        "input_tokens": perfile_input_tokens,
        "output_tokens": perfile_output_tokens,
        "output_file": str(output_file_path) if output_file_path else None,
        "error": error_message,
        "name": original_display_name,
    }

# --- Main Script ---
def main():
    global total_start_time
    total_start_time = time.time()

    # 1. 清除代理环境变量（豆包API不需要代理）
    clear_proxy_environment()

    # 2. 设置日志系统
    realtime_log_file, results_log_file = setup_logging()
    results_logger = create_results_logger(results_log_file)
    logging.info("开始执行视频分析脚本...")
    logging.info(f"实时日志文件: {realtime_log_file}")
    logging.info(f"结果日志文件: {results_log_file}")

    # 火山引擎豆包API不需要额外配置，直接在调用时初始化
    logging.info("豆包 API 配置完成")

    # 扫描当前目录及其子目录
    current_directory = Path(".").resolve()
    video_files = find_video_files(current_directory)

    if not video_files:
        print(f"在目录 {current_directory} 及其子目录中未找到视频文件。")
        return

    print(f"\n找到 {len(video_files)} 个视频文件:")
    for i, vf_path in enumerate(video_files):
        print(f"  {i+1}. {vf_path}")

    # 处理视频文件
    for video_path_original in video_files:
            print(f"\n========== 开始处理原始视频: {video_path_original.resolve()} ==========")

            video_to_process = video_path_original
            original_display_name = video_path_original.name
            converted_mp4_path = None
            output_dir = video_path_original.parent

            # M4V转换逻辑保持不变
            if video_path_original.suffix.lower() == '.m4v':
                print(f"  检测到 M4V 文件: {original_display_name}。正在尝试转换为 MP4...")
                converted_mp4_path = video_path_original.with_name(f"{video_path_original.stem}_converted_to.mp4")

                ffmpeg_command = [
                    "ffmpeg", "-y",
                    "-i", str(video_path_original),
                    "-c:v", "copy",
                    "-c:a", "copy",
                    str(converted_mp4_path)
                ]
                try:
                    subprocess.run(ffmpeg_command, check=True, capture_output=True, text=True, encoding='utf-8')
                    print(f"  M4V 文件已成功转换为 MP4: {converted_mp4_path}")
                    video_to_process = converted_mp4_path
                except subprocess.CalledProcessError as e:
                    error_msg_ffmpeg = f"FFmpeg 转换失败: {original_display_name}. Error: {e.stderr}"
                    print(f"  错误: {error_msg_ffmpeg}")
                    error_file_path = output_dir / f"{video_path_original.stem}_conversion_error.txt"
                    try:
                        with open(error_file_path, "w", encoding="utf-8") as f:
                            f.write(f"FFmpeg 转换视频 {original_display_name} 时出错:\n{e.stderr}")
                        print(f"  FFmpeg 错误详情已保存到: {error_file_path}")
                    except IOError as e_io:
                        print(f"  无法写入 FFmpeg 错误文件 {error_file_path}: {e_io}")
                    print(f"========== 完成处理原始视频: {video_path_original.resolve()} (转换失败) ==========")
                    continue
                except FileNotFoundError:
                    error_msg_ffmpeg_nf = f"FFmpeg 未找到。请确保 FFmpeg 已安装并添加到系统 PATH。跳过转换 {original_display_name}。"
                    print(f"  错误: {error_msg_ffmpeg_nf}")
                    error_file_path = output_dir / f"{video_path_original.stem}_ffmpeg_not_found.txt"
                    try:
                        with open(error_file_path, "w", encoding="utf-8") as f:
                            f.write(error_msg_ffmpeg_nf)
                        print(f"  FFmpeg 未找到错误已保存到: {error_file_path}")
                    except IOError as e_io:
                        print(f"  无法写入 FFmpeg 未找到错误文件 {error_file_path}: {e_io}")
                    print(f"========== 完成处理原始视频: {video_path_original.resolve()} (FFmpeg 未找到) ==========")
                    continue

            try:
                print(f"  将使用文件进行分析: {video_to_process.resolve()} (原始文件名: {original_display_name})")
                analysis_result, error_message, _, _ = analyze_video_with_doubao(video_to_process, original_display_name, use_upload=True)

                if error_message:
                    print(f"分析原始视频 {original_display_name} 时出错: {error_message}")
                    error_file_path = output_dir / f"{video_path_original.stem}_analysis_error.txt"
                    try:
                        with open(error_file_path, "w", encoding="utf-8") as f:
                            f.write(f"分析视频 {original_display_name} (处理文件: {video_to_process.name}) 时出错:\n{error_message}")
                        print(f"  错误详情已保存到: {error_file_path}")
                    except IOError as e_io:
                        print(f"  无法写入分析错误文件 {error_file_path}: {e_io}")
                elif analysis_result:
                    output_file_path = output_dir / f"{video_path_original.stem}_analysis.json"
                    try:
                        with open(output_file_path, "w", encoding="utf-8") as f:
                            f.write(f"文件路径: {video_path_original.resolve()}\n")
                            f.write(analysis_result)
                        print(f"  分析结果已成功保存到: {output_file_path}")
                    except IOError as e_io:
                        print(f"  写入分析结果到文件 {output_file_path} 时出错: {e_io}")
                else:
                    print(f"  未收到原始视频 {original_display_name} 的分析结果。")
                    empty_file_path = output_dir / f"{video_path_original.stem}_analysis_empty.txt"
                    try:
                        with open(empty_file_path, "w", encoding="utf-8") as f:
                            f.write(f"未从豆包收到原始视频 {original_display_name} (处理文件: {video_to_process.name}) 的分析结果。")
                        print(f"  空分析记录已保存到: {empty_file_path}")
                    except IOError as e_io:
                        print(f"  无法写入空分析记录文件 {empty_file_path}: {e_io}")

            finally:
                if converted_mp4_path and converted_mp4_path.exists():
                    try:
                        os.remove(converted_mp4_path)
                        print(f"  已清理转换后的文件: {converted_mp4_path}")
                    except OSError as e:
                        print(f"  无法删除转换后的文件 {converted_mp4_path}: {e}")
                print(f"========== 完成处理原始视频: {video_path_original.resolve()} ==========")

    print("\n所有视频处理完毕。")

    # 在脚本结束时输出统计信息
    total_duration = time.time() - total_start_time
    logging.info("\n" + "="*50)
    logging.info("运行统计信息:")
    logging.info(f"总耗时: {total_duration:.2f} 秒")
    logging.info(f"总输入Token量: {total_input_tokens}")
    logging.info(f"总输出Token量: {total_output_tokens}")
    logging.info("="*50)

    print(f"\n运行完成！统计信息:")
    print(f"总耗时: {total_duration:.2f} 秒")
    print(f"总输入Token量: {total_input_tokens}")
    print(f"总输出Token量: {total_output_tokens}")
    print(f"详细日志已保存到: {realtime_log_file}")

    if not video_files:
        print(f"在目录 {current_directory.resolve()} 及其子目录中未找到视频文件。")
        return

    print(f"\n找到 {len(video_files)} 个视频文件:")
    for i, vf_path in enumerate(video_files):
        print(f"  {i+1}. {vf_path}")

    for video_path_original in video_files:
        print(f"\n========== 开始处理原始视频: {video_path_original.resolve()} ==========")

        video_to_process = video_path_original
        original_display_name = video_path_original.name
        converted_mp4_path = None
        output_dir = video_path_original.parent # Output relative to original file

        if video_path_original.suffix.lower() == '.m4v':
            print(f"  检测到 M4V 文件: {original_display_name}。正在尝试转换为 MP4...")
            converted_mp4_path = video_path_original.with_name(f"{video_path_original.stem}_converted_to.mp4")

            ffmpeg_command = [
                "ffmpeg", "-y",
                "-i", str(video_path_original),
                "-c:v", "copy",
                "-c:a", "copy",
                str(converted_mp4_path)
            ]
            try:
                subprocess.run(ffmpeg_command, check=True, capture_output=True, text=True, encoding='utf-8')
                print(f"  M4V 文件已成功转换为 MP4: {converted_mp4_path}")
                video_to_process = converted_mp4_path
            except subprocess.CalledProcessError as e:
                error_msg_ffmpeg = f"FFmpeg 转换失败: {original_display_name}. Error: {e.stderr}"
                print(f"  错误: {error_msg_ffmpeg}")
                error_file_path = output_dir / f"{video_path_original.stem}_conversion_error.txt"
                try:
                    with open(error_file_path, "w", encoding="utf-8") as f:
                        f.write(f"FFmpeg 转换视频 {original_display_name} 时出错:\n{e.stderr}")
                    print(f"  FFmpeg 错误详情已保存到: {error_file_path}")
                except IOError as e_io:
                    print(f"  无法写入 FFmpeg 错误文件 {error_file_path}: {e_io}")
                print(f"========== 完成处理原始视频: {video_path_original.resolve()} (转换失败) ==========")
                continue
            except FileNotFoundError:
                error_msg_ffmpeg_nf = f"FFmpeg 未找到。请确保 FFmpeg 已安装并添加到系统 PATH。跳过转换 {original_display_name}。"
                print(f"  错误: {error_msg_ffmpeg_nf}")
                error_file_path = output_dir / f"{video_path_original.stem}_ffmpeg_not_found.txt"
                try:
                    with open(error_file_path, "w", encoding="utf-8") as f:
                        f.write(error_msg_ffmpeg_nf)
                    print(f"  FFmpeg 未找到错误已保存到: {error_file_path}")
                except IOError as e_io:
                    print(f"  无法写入 FFmpeg 未找到错误文件 {error_file_path}: {e_io}")
                print(f"========== 完成处理原始视频: {video_path_original.resolve()} (FFmpeg 未找到) ==========")
                continue

        try:
            print(f"  将使用文件进行分析: {video_to_process.resolve()} (原始文件名: {original_display_name})")
            analysis_result, error_message, _, _ = analyze_video_with_doubao(video_to_process, original_display_name)

            if error_message:
                print(f"分析原始视频 {original_display_name} 时出错: {error_message}")
                error_file_path = output_dir / f"{video_path_original.stem}_analysis_error.txt"
                try:
                    with open(error_file_path, "w", encoding="utf-8") as f:
                        f.write(f"分析视频 {original_display_name} (处理文件: {video_to_process.name}) 时出错:\n{error_message}")
                    print(f"  错误详情已保存到: {error_file_path}")
                except IOError as e_io:
                    print(f"  无法写入分析错误文件 {error_file_path}: {e_io}")
            elif analysis_result:
                output_file_path = output_dir / f"{video_path_original.stem}_analysis.json" # 文件扩展名已更改为 .json
                try:
                    with open(output_file_path, "w", encoding="utf-8") as f:
                        f.write(f"文件路径: {video_path_original.resolve()}\n") # 在 JSON 内容前添加文件路径
                        f.write(analysis_result)
                    print(f"  分析结果已成功保存到: {output_file_path}")
                except IOError as e_io:
                    print(f"  写入分析结果到文件 {output_file_path} 时出错: {e_io}")
            else:
                # This case should ideally be covered by error_message, but as a fallback
                print(f"  未收到原始视频 {original_display_name} 的分析结果。")
                empty_file_path = output_dir / f"{video_path_original.stem}_analysis_empty.txt"
                try:
                    with open(empty_file_path, "w", encoding="utf-8") as f:
                        f.write(f"未从 Gemini 收到原始视频 {original_display_name} (处理文件: {video_to_process.name}) 的分析结果。")
                    print(f"  空分析记录已保存到: {empty_file_path}")
                except IOError as e_io:
                    print(f"  无法写入空分析记录文件 {empty_file_path}: {e_io}")

        finally:
            if converted_mp4_path and converted_mp4_path.exists():
                try:
                    os.remove(converted_mp4_path)
                    print(f"  已清理转换后的文件: {converted_mp4_path}")
                except OSError as e:
                    print(f"  无法删除转换后的文件 {converted_mp4_path}: {e}")
            print(f"========== 完成处理原始视频: {video_path_original.resolve()} ==========")


    print("\n所有视频处理完毕。")

    # 在脚本结束时输出统计信息
    total_duration = time.time() - total_start_time
    logging.info("\n" + "="*50)
    logging.info("运行统计信息:")
    logging.info(f"总耗时: {total_duration:.2f} 秒")
    logging.info(f"总输入Token量: {total_input_tokens}")
    logging.info(f"总输出Token量: {total_output_tokens}")
    logging.info("="*50)

    print(f"\n运行完成！统计信息:")
    print(f"总耗时: {total_duration:.2f} 秒")
    print(f"总输入Token量: {total_input_tokens}")
    print(f"总输出Token量: {total_output_tokens}")
    print(f"详细日志已保存到: {realtime_log_file}")

if __name__ == "__main__":
    main()
