# GitHub配置文件模板
# 请复制此文件为 github_config.py 并填写您的信息

# GitHub Personal Access Token
# 获取方法: GitHub Settings -> Developer settings -> Personal access tokens
GITHUB_TOKEN = "your_github_token_here"

# GitHub用户名
GITHUB_USERNAME = "your_username_here"

# 仓库名称（用于托管视频文件）
GITHUB_REPO = "video-hosting"

# 示例:
# GITHUB_TOKEN = "ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
# GITHUB_USERNAME = "john_doe"
# GITHUB_REPO = "video-hosting"
