#!/usr/bin/env python3
"""
视频文件上传服务 - 支持多种临时文件托管方案
"""

import requests
import json
from pathlib import Path
from typing import Optional, Tuple
import logging

class VideoUploader:
    """视频文件上传器，支持多种服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def upload_to_file_io(self, video_file: Path) -> Optional[str]:
        """上传到file.io服务"""
        try:
            with open(video_file, 'rb') as f:
                files = {'file': f}
                response = requests.post('https://file.io', files=files, timeout=60)
                
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    url = result.get('link')
                    self.logger.info(f"文件已上传到file.io: {url}")
                    return url
            
            self.logger.error(f"file.io上传失败: {response.text}")
            return None
            
        except Exception as e:
            self.logger.error(f"file.io上传异常: {e}")
            return None
    
    def upload_to_0x0(self, video_file: Path) -> Optional[str]:
        """上传到0x0.st服务"""
        try:
            with open(video_file, 'rb') as f:
                files = {'file': f}
                response = requests.post('https://0x0.st', files=files, timeout=60)
                
            if response.status_code == 200:
                url = response.text.strip()
                self.logger.info(f"文件已上传到0x0.st: {url}")
                return url
            
            self.logger.error(f"0x0.st上传失败: {response.text}")
            return None
            
        except Exception as e:
            self.logger.error(f"0x0.st上传异常: {e}")
            return None
    
    def upload_to_catbox(self, video_file: Path) -> Optional[str]:
        """上传到catbox.moe服务"""
        try:
            with open(video_file, 'rb') as f:
                files = {'fileToUpload': f}
                data = {'reqtype': 'fileupload'}
                response = requests.post('https://catbox.moe/user/api.php', 
                                       files=files, data=data, timeout=60)
                
            if response.status_code == 200:
                url = response.text.strip()
                if url.startswith('https://'):
                    self.logger.info(f"文件已上传到catbox.moe: {url}")
                    return url
            
            self.logger.error(f"catbox.moe上传失败: {response.text}")
            return None
            
        except Exception as e:
            self.logger.error(f"catbox.moe上传异常: {e}")
            return None
    
    def upload_with_fallback(self, video_file: Path) -> Tuple[Optional[str], str]:
        """尝试多个服务，返回第一个成功的URL"""
        services = [
            ("file.io", self.upload_to_file_io),
            ("0x0.st", self.upload_to_0x0),
            ("catbox.moe", self.upload_to_catbox)
        ]
        
        for service_name, upload_func in services:
            self.logger.info(f"尝试上传到 {service_name}...")
            url = upload_func(video_file)
            if url:
                return url, service_name
        
        return None, "所有服务都失败"

def test_upload():
    """测试上传功能"""
    uploader = VideoUploader()
    
    # 查找视频文件
    video_files = []
    for ext in ['.mp4', '.mov', '.avi']:
        video_files.extend(Path('.').glob(f"*{ext}"))
    
    if not video_files:
        print("未找到视频文件")
        return
    
    video_file = video_files[0]
    print(f"测试上传文件: {video_file}")
    
    # 检查文件大小
    file_size = video_file.stat().st_size / (1024 * 1024)  # MB
    print(f"文件大小: {file_size:.2f} MB")
    
    if file_size > 100:
        print("警告: 文件较大，上传可能需要较长时间")
    
    # 尝试上传
    url, service = uploader.upload_with_fallback(video_file)
    
    if url:
        print(f"✅ 上传成功!")
        print(f"服务: {service}")
        print(f"URL: {url}")
        
        # 验证URL是否可访问
        try:
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                print("✅ URL验证成功，可以访问")
            else:
                print(f"⚠️ URL验证失败: {response.status_code}")
        except Exception as e:
            print(f"⚠️ URL验证异常: {e}")
            
    else:
        print(f"❌ 上传失败: {service}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_upload()
